-- Migration: Create outbox_events table for Assessment Service
-- This table implements the Outbox Pattern for reliable event publishing

-- Create assessment schema if not exists
CREATE SCHEMA IF NOT EXISTS assessment;

-- Drop table if exists (for development only)
-- DROP TABLE IF EXISTS assessment.outbox_events;

-- Create outbox_events table
CREATE TABLE IF NOT EXISTS assessment.outbox_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  aggregate_id UUID NOT NULL,
  aggregate_type VARCHAR(50) NOT NULL,
  event_type VARCHAR(100) NOT NULL,
  event_version INTEGER NOT NULL DEFAULT 1,
  payload JSONB NOT NULL,
  metadata JSONB,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  processed_at TIMESTAMP NULL,
  failed_at TIMESTAMP NULL,
  retry_count INTEGER NOT NULL DEFAULT 0,
  max_retries INTEGER NOT NULL DEFAULT 5,
  error_message TEXT,
  routing_key VARCHAR(100) NOT NULL,
  exchange VARCHAR(100) NOT NULL DEFAULT 'atma_events'
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_outbox_events_unprocessed 
ON assessment.outbox_events (processed_at, failed_at, retry_count, max_retries)
WHERE processed_at IS NULL AND failed_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_outbox_events_retry 
ON assessment.outbox_events (failed_at, retry_count, max_retries, created_at);

CREATE INDEX IF NOT EXISTS idx_outbox_events_aggregate 
ON assessment.outbox_events (aggregate_type, aggregate_id, created_at);

CREATE INDEX IF NOT EXISTS idx_outbox_events_type 
ON assessment.outbox_events (event_type, created_at);

CREATE INDEX IF NOT EXISTS idx_outbox_events_created 
ON assessment.outbox_events (created_at);

-- Add comments for documentation
COMMENT ON TABLE assessment.outbox_events IS 'Outbox pattern table for reliable event publishing';
COMMENT ON COLUMN assessment.outbox_events.aggregate_id IS 'ID of the aggregate that generated this event (e.g., job_id, user_id)';
COMMENT ON COLUMN assessment.outbox_events.aggregate_type IS 'Type of aggregate (e.g., Job, User, Assessment)';
COMMENT ON COLUMN assessment.outbox_events.event_type IS 'Type of event (e.g., JobCreated, AnalysisRequested)';
COMMENT ON COLUMN assessment.outbox_events.event_version IS 'Version of the event schema for backward compatibility';
COMMENT ON COLUMN assessment.outbox_events.payload IS 'Event payload containing all necessary data';
COMMENT ON COLUMN assessment.outbox_events.metadata IS 'Additional metadata like correlation_id, user_id, etc.';
COMMENT ON COLUMN assessment.outbox_events.processed_at IS 'When the event was successfully published to message broker';
COMMENT ON COLUMN assessment.outbox_events.failed_at IS 'When the event processing failed';
COMMENT ON COLUMN assessment.outbox_events.retry_count IS 'Number of times processing was attempted';
COMMENT ON COLUMN assessment.outbox_events.max_retries IS 'Maximum number of retry attempts';
COMMENT ON COLUMN assessment.outbox_events.error_message IS 'Last error message if processing failed';
COMMENT ON COLUMN assessment.outbox_events.routing_key IS 'RabbitMQ routing key for this event';
COMMENT ON COLUMN assessment.outbox_events.exchange IS 'RabbitMQ exchange for this event';

-- Grant permissions (adjust as needed)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON assessment.outbox_events TO assessment_service_user;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA assessment TO assessment_service_user;
