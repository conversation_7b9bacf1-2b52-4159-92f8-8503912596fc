# Server Configuration
PORT=3003
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production

# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
QUEUE_NAME=assessment_analysis
EXCHANGE_NAME=atma_exchange
ROUTING_KEY=analysis.process

# Auth Service Configuration
AUTH_SERVICE_URL=http://localhost:3001

# Archive Service Configuration
ARCHIVE_SERVICE_URL=http://localhost:3002/archive

# Token Cost Configuration
ANALYSIS_TOKEN_COST=1

# Queue Configuration
QUEUE_DURABLE=true
MESSAGE_PERSISTENT=true

# Database Configuration (Now Active for Outbox Pattern)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_db
DB_USER=postgres
DB_PASSWORD=password
DB_DIALECT=postgres
DB_SCHEMA=assessment
DB_POOL_MAX=20
DB_POOL_MIN=5
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000
DB_POOL_EVICT=5000

# Outbox Pattern Configuration
OUTBOX_PROCESSING_INTERVAL=5000
OUTBOX_BATCH_SIZE=50
OUTBOX_MAX_RETRIES=3

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/assessment-service.log

# Internal Service Configuration
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
