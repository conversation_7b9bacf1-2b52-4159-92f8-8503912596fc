const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const path = require('path');

// Load environment variables
require('dotenv').config();

// Import routes
const assessmentRoutes = require('./routes/assessments');
const healthRoutes = require('./routes/health');

// Import utilities
const logger = require('./utils/logger');
const { errorHandler } = require('./middleware/errorHandler');

// Import services
const queueService = require('./services/queueService');
const database = require('./config/database');
const OutboxProcessor = require('./services/outboxProcessor');

// Create Express app
const app = express();
const PORT = process.env.PORT || 3003;

// Middleware - CORS unlimited access
app.use(cors({
  origin: true, // Allow all origins
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(morgan('combined', {
  stream: {
    write: (message) => logger.info(message.trim())
  }
}));

// Routes
app.use('/assessments', assessmentRoutes);
app.use('/health', healthRoutes);

// Testing endpoint (only in development)
if (process.env.NODE_ENV === 'development') {
  const testRoutes = require('./routes/test');
  app.use('/test', testRoutes);
}

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'ATMA Assessment Service is running',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `Route ${req.method} ${req.originalUrl} not found`
    }
  });
});

// Error handling middleware
app.use(errorHandler);

// Initialize services
const initializeServices = async() => {
  let outboxProcessor = null;

  try {
    logger.info('Initializing Assessment Service...');

    // Initialize database
    await database.initialize();
    logger.info('Database initialized');

    // Initialize queue service
    await queueService.initialize();
    logger.info('Queue service initialized');

    // Initialize outbox processor
    outboxProcessor = new OutboxProcessor();
    await outboxProcessor.initialize();
    logger.info('Outbox processor initialized');

    // Store outbox processor for graceful shutdown
    app.locals.outboxProcessor = outboxProcessor;

    logger.info('Assessment Service initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize Assessment Service', { error: error.message });

    // Cleanup on initialization failure
    if (outboxProcessor) {
      try {
        await outboxProcessor.shutdown();
      } catch (cleanupError) {
        logger.error('Error during cleanup', { error: cleanupError.message });
      }
    }

    process.exit(1);
  }
};

// Graceful shutdown
const gracefulShutdown = async() => {
  logger.info('Shutting down gracefully...');
  try {
    // Shutdown outbox processor
    if (app.locals.outboxProcessor) {
      await app.locals.outboxProcessor.shutdown();
      logger.info('Outbox processor closed');
    }

    // Close queue service
    await queueService.close();
    logger.info('Queue service closed');

    // Close database
    await database.close();
    logger.info('Database closed');

    logger.info('All services closed successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown', { error: error.message });
    process.exit(1);
  }
};

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// Start server
if (process.env.NODE_ENV !== 'test') {
  initializeServices().then(() => {
    app.listen(PORT, () => {
      logger.info(`Assessment Service running on port ${PORT}`);
      logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
    });
  });
}

module.exports = app;
